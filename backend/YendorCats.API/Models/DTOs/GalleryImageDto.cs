using System;

namespace YendorCats.API.Models.DTOs
{
    /// <summary>
    /// DTO for gallery image API responses with optimized data structure
    /// Designed for high-performance API responses with minimal data transfer
    /// </summary>
    public class GalleryImageDto
    {
        public long Id { get; set; }
        
        /// <summary>
        /// Storage key for the image (S3 key or B2 key)
        /// </summary>
        public string StorageKey { get; set; } = string.Empty;
        
        /// <summary>
        /// Public URL for direct image access
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Thumbnail URL for optimized loading
        /// </summary>
        public string? ThumbnailUrl { get; set; }
        
        /// <summary>
        /// Original filename for download purposes
        /// </summary>
        public string OriginalFileName { get; set; } = string.Empty;
        
        // Cat information
        public string? CatName { get; set; }
